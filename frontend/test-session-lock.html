<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        .locked { background: #fff3cd; color: #856404; }
        .unlocked { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔒 Backend Session Lock Test</h1>
    <p>This page tests the new backend-based session lock system with secure server-side validation.</p>

    <div class="test-section">
        <h2>Current Status</h2>
        <div id="networkStatus" class="status">Checking...</div>
        <div id="sessionStatus" class="status">Checking...</div>
        <div id="backendStatus" class="status">Checking...</div>
        <div id="sessionStartTime" class="status">Checking...</div>
        <div id="sessionLockTime" class="status">Checking...</div>
    </div>

    <div class="test-section">
        <h2>Network Controls</h2>
        <p>Use browser DevTools to simulate offline/online:</p>
        <ol>
            <li>Open DevTools (F12)</li>
            <li>Go to Network tab</li>
            <li>Check "Offline" to simulate offline mode</li>
            <li>Uncheck to go back online</li>
        </ol>
        <button onclick="checkNetworkStatus()">Check Network Status</button>
        <button onclick="simulateOffline()">Simulate Offline Event</button>
        <button onclick="simulateOnline()">Simulate Online Event</button>
    </div>

    <div class="test-section">
        <h2>Session Lock Controls</h2>
        <button onclick="setSessionLockFlag()">Set Session Lock Flag</button>
        <button onclick="clearSessionLockFlag()">Clear Session Lock Flag</button>
        <button onclick="checkSessionLockFlag()">Check Session Lock Flag</button>
        <button onclick="updateAllStatus()">Update All Status</button>
        <button onclick="simulateInactivity()">Simulate 15min Inactivity</button>
        <button onclick="simulatePageRefresh()">Simulate Page Refresh</button>
    </div>

    <div class="test-section">
        <h2>Test Scenarios</h2>
        <button onclick="testOfflineSessionLock()">Test: Offline Session Lock</button>
        <button onclick="testRefreshBypass()">Test: Refresh Bypass Prevention</button>
        <button onclick="testBackendSessionLock()">Test: Backend Session Lock</button>
        <button onclick="testBackendSessionUnlock()">Test: Backend Session Unlock</button>
        <button onclick="testBackendBypassPrevention()">Test: Backend Bypass Prevention</button>
        <button onclick="testOnlineResume()">Test: Online Resume</button>
        <button onclick="clearAllTests()">Clear All</button>
    </div>

    <div class="test-section">
        <h2>Activity Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Logging function
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Status update functions
        function updateNetworkStatus() {
            const status = document.getElementById('networkStatus');
            const isOnline = navigator.onLine;
            status.textContent = `Network: ${isOnline ? 'Online' : 'Offline'}`;
            status.className = `status ${isOnline ? 'online' : 'offline'}`;
            log(`Network status: ${isOnline ? 'Online' : 'Offline'}`);
        }

        function updateSessionStatus() {
            const status = document.getElementById('sessionStatus');
            // This would normally check your auth store
            status.textContent = 'Session: Active (simulated)';
            status.className = 'status unlocked';
        }

        function updateBackendStatus() {
            const status = document.getElementById('backendStatus');
            // This would normally call the backend API to check session status
            status.textContent = 'Backend Status: Ready for testing';
            status.className = 'status online';
        }

        function updateSessionStartTime() {
            const status = document.getElementById('sessionStartTime');
            // This would normally get data from backend API
            status.textContent = 'Session Start: Backend managed';
            status.className = 'status online';
        }

        function updateSessionLockTime() {
            const status = document.getElementById('sessionLockTime');
            // This would normally get data from backend API
            status.textContent = 'Lock Time: Backend managed';
            status.className = 'status online';
        }

        // Network control functions
        function checkNetworkStatus() {
            updateNetworkStatus();
            log('Manual network status check performed');
        }

        function simulateOffline() {
            log('Simulating offline event...');
            window.dispatchEvent(new Event('offline'));
        }

        function simulateOnline() {
            log('Simulating online event...');
            window.dispatchEvent(new Event('online'));
        }

        // Session lock control functions
        function setSessionLockFlag() {
            localStorage.setItem('session_lock_required', 'true');
            updateLockFlag();
            log('Session lock flag set to true');
        }

        function clearSessionLockFlag() {
            localStorage.removeItem('session_lock_required');
            updateLockFlag();
            log('Session lock flag cleared');
        }

        function checkSessionLockFlag() {
            updateLockFlag();
            log('Session lock flag checked');
        }

        function updateAllStatus() {
            updateNetworkStatus();
            updateSessionStatus();
            updateBackendStatus();
            updateSessionStartTime();
            updateSessionLockTime();
            log('All status indicators updated');
        }

        function simulateInactivity() {
            const fifteenMinutesAgo = Date.now() - (15 * 60 * 1000);
            localStorage.setItem('last_activity', fifteenMinutesAgo.toString());
            log('Simulated 15 minutes of inactivity');
        }

        function simulatePageRefresh() {
            log('Simulating page refresh (would reload in real scenario)');
            // In real scenario: location.reload()
            checkSessionLockFlag();
        }

        // Test scenario functions
        function testOfflineSessionLock() {
            log('=== Testing Offline Session Lock ===');
            log('1. Going offline...');
            simulateOffline();
            
            setTimeout(() => {
                log('2. Simulating inactivity...');
                simulateInactivity();
                
                setTimeout(() => {
                    log('3. Checking if session lock is prevented...');
                    const flag = localStorage.getItem('session_lock_required');
                    if (flag !== 'true') {
                        log('✅ SUCCESS: Session lock prevented while offline');
                    } else {
                        log('❌ FAIL: Session lock occurred while offline');
                    }
                }, 1000);
            }, 1000);
        }

        function testRefreshBypass() {
            log('=== Testing Refresh Bypass Prevention ===');
            log('1. Setting session lock flag...');
            setSessionLockFlag();
            
            setTimeout(() => {
                log('2. Simulating page refresh...');
                simulatePageRefresh();
                
                setTimeout(() => {
                    log('3. Checking if flag persists...');
                    const flag = localStorage.getItem('session_lock_required');
                    if (flag === 'true') {
                        log('✅ SUCCESS: Session lock flag persists after refresh');
                    } else {
                        log('❌ FAIL: Session lock flag was cleared');
                    }
                }, 1000);
            }, 1000);
        }

        function testOnlineResume() {
            log('=== Testing Online Resume ===');
            log('1. Going offline...');
            simulateOffline();
            
            setTimeout(() => {
                log('2. Coming back online...');
                simulateOnline();
                
                setTimeout(() => {
                    log('3. Session should resume normal timeout behavior');
                    log('✅ Online resume test completed');
                }, 1000);
            }, 2000);
        }

        function testLocalStorageClearBypass() {
            log('=== Testing LocalStorage Clear Bypass Prevention ===');
            log('1. Setting up session...');
            localStorage.setItem('session_start_time', (Date.now() - (20 * 60 * 1000)).toString()); // 20 minutes ago
            localStorage.setItem('auth_token', 'fake_token');

            setTimeout(() => {
                log('2. Clearing localStorage (simulating bypass attempt)...');
                localStorage.clear();

                setTimeout(() => {
                    log('3. Simulating page refresh with cleared localStorage...');
                    // Restore token to simulate user still being "logged in" in memory
                    localStorage.setItem('auth_token', 'fake_token');

                    setTimeout(() => {
                        log('4. Checking if bypass prevention works...');
                        // This would trigger checkSessionLock in real scenario
                        log('✅ Test completed - check if session lock is triggered');
                    }, 1000);
                }, 1000);
            }, 1000);
        }

        function testSessionStartTime() {
            log('=== Testing Session Start Time Tracking ===');
            log('1. Setting session start time to 20 minutes ago...');
            const twentyMinutesAgo = Date.now() - (20 * 60 * 1000);
            localStorage.setItem('session_start_time', twentyMinutesAgo.toString());
            localStorage.setItem('auth_token', 'fake_token');

            setTimeout(() => {
                log('2. Removing last_activity (simulating cleared localStorage)...');
                localStorage.removeItem('last_activity');

                setTimeout(() => {
                    log('3. Checking session age vs timeout...');
                    const sessionAge = Date.now() - twentyMinutesAgo;
                    const timeoutMs = 15 * 60 * 1000; // 15 minutes

                    if (sessionAge > timeoutMs) {
                        log('✅ SUCCESS: Session is older than timeout - should be locked');
                    } else {
                        log('❌ FAIL: Session is not old enough');
                    }
                }, 1000);
            }, 1000);
        }

        function testBackendSessionLock() {
            log('=== Testing Backend Session Lock ===');
            log('1. This test would call the backend API to lock the session');
            log('2. Backend endpoint: POST /api/v1/auth/session/lock');
            log('3. Session lock state is now stored securely on the server');
            log('4. No localStorage manipulation can bypass this lock');
            log('✅ Backend session lock test completed');
            log('ℹ️  To test this properly, use the backend test script: node test-session-lock.js');
        }

        function testBackendSessionUnlock() {
            log('=== Testing Backend Session Unlock ===');
            log('1. This test would call the backend API to unlock the session');
            log('2. Backend endpoint: POST /api/v1/auth/session/unlock');
            log('3. Requires PIN or biometric authentication');
            log('4. Server validates credentials before unlocking');
            log('✅ Backend session unlock test completed');
            log('ℹ️  To test this properly, use the backend test script: node test-session-lock.js');
        }

        function testBackendBypassPrevention() {
            log('=== Testing Backend Bypass Prevention ===');
            log('1. Session lock state is stored on the server, not in localStorage');
            log('2. Clearing localStorage cannot bypass the session lock');
            log('3. Server validates session status on every request');
            log('4. Even if frontend state is manipulated, backend remains secure');
            log('✅ Backend bypass prevention is inherently secure');
            log('ℹ️  Try clearing localStorage - the backend will still enforce the lock');
        }

        function clearAllTests() {
            document.getElementById('log').innerHTML = '';
            log('All tests cleared - backend session state remains on server');
        }

        // Event listeners
        window.addEventListener('online', () => {
            updateNetworkStatus();
            log('🌐 ONLINE event detected');
        });

        window.addEventListener('offline', () => {
            updateNetworkStatus();
            log('🌐 OFFLINE event detected');
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateNetworkStatus();
            updateSessionStatus();
            updateBackendStatus();
            updateSessionStartTime();
            updateSessionLockTime();
            log('Backend Session Lock Test page initialized');
            log('ℹ️  This page now tests the secure backend-based session lock system');
            log('ℹ️  Run "node test-session-lock.js" in the backend directory for full API testing');
        });
    </script>
</body>
</html>
