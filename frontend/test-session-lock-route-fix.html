<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Route Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .test-section {
            border: 2px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .route-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>✅ Session Lock Route Fix Verification</h1>
    <p>This test verifies that session lock dialog only appears on protected routes.</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div class="route-info">
            <div>Current URL: <span id="currentUrl"></span></div>
            <div>Is Protected Route: <span id="isProtected"></span></div>
            <div>Session Lock State: <span id="lockState"></span></div>
            <div>Should Show Dialog: <span id="shouldShow"></span></div>
        </div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container test-section">
        <h2>🧪 Route-Based Dialog Test</h2>
        <div>
            <button onclick="testCompleteScenario()">🚀 Run Complete Test</button>
            <button onclick="lockSession()">🔒 Lock Session</button>
            <button onclick="unlockSession()">🔓 Unlock Session</button>
        </div>
        <div>
            <button onclick="navigateTo('/')">Go to Home (/)</button>
            <button onclick="navigateTo('/about')">Go to About (/about)</button>
            <button onclick="navigateTo('/admin')">Go to Admin (/admin)</button>
            <button onclick="navigateTo('/dashboard')">Go to Dashboard (/dashboard)</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************.iHnO_Rbiocv_DcHNMtNBuzpatFAhxnBC9DGdU6GTIi4';

        // Protected routes (same as frontend)
        const protectedRoutes = ['/admin', '/dashboard', '/profile', '/settings', '/account'];

        // Check if route is protected
        function isProtectedRoute(path = window.location.pathname) {
            return protectedRoutes.some(route => path.startsWith(route));
        }

        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // API helper function
        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json',
                    'Authorization': `Bearer ${authToken}`
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        // Update status display
        function updateStatus() {
            const currentPath = window.location.pathname;
            const isProtected = isProtectedRoute(currentPath);
            
            document.getElementById('currentUrl').textContent = currentPath;
            document.getElementById('isProtected').textContent = isProtected ? 'YES' : 'NO';
            document.getElementById('isProtected').style.color = isProtected ? '#dc3545' : '#28a745';
            
            // Check session lock state
            checkSessionLockState();
        }

        // Check current session lock state
        async function checkSessionLockState() {
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                const isProtected = isProtectedRoute();
                const shouldShow = isLocked && isProtected;
                
                document.getElementById('lockState').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockState').style.color = isLocked ? '#dc3545' : '#28a745';
                
                document.getElementById('shouldShow').textContent = shouldShow ? 'YES' : 'NO';
                document.getElementById('shouldShow').style.color = shouldShow ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockState').textContent = 'ERROR';
                document.getElementById('lockState').style.color = '#6c757d';
                document.getElementById('shouldShow').textContent = 'ERROR';
                document.getElementById('shouldShow').style.color = '#6c757d';
                return null;
            }
        }

        // Navigate to a route
        function navigateTo(path) {
            log(`🔄 Navigating to: ${path}`);
            history.pushState({}, '', path);
            updateStatus();
            log(`📍 Navigation complete. Protected: ${isProtectedRoute(path)}`);
        }

        // Lock session
        async function lockSession() {
            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        // Unlock session
        async function unlockSession() {
            log('🔓 Unlocking session...');
            try {
                await apiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    pin: '123456',
                    deviceFingerprint: 'test-device'
                });
                log('✅ Session unlocked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to unlock session: ${error.message}`);
            }
        }

        // Run complete test scenario
        async function testCompleteScenario() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🚀 RUNNING COMPLETE SESSION LOCK ROUTE TEST ===');
            testResult.textContent = 'Testing in progress...';
            testResult.className = 'status warning';
            
            let allTestsPassed = true;
            
            try {
                // Test 1: Lock session on public route
                log('Test 1: Navigate to home page and lock session');
                navigateTo('/');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await lockSession();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const isLockedOnPublic = await checkSessionLockState();
                if (isLockedOnPublic) {
                    log('✅ Test 1 PASSED: Session locked on public route, dialog should NOT show');
                } else {
                    log('❌ Test 1 FAILED: Session not locked');
                    allTestsPassed = false;
                }
                
                // Test 2: Navigate to protected route with locked session
                log('Test 2: Navigate to admin page with locked session');
                navigateTo('/admin');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const isLockedOnProtected = await checkSessionLockState();
                if (isLockedOnProtected) {
                    log('✅ Test 2 PASSED: Session locked on protected route, dialog SHOULD show');
                } else {
                    log('❌ Test 2 FAILED: Session not locked on protected route');
                    allTestsPassed = false;
                }
                
                // Test 3: Navigate back to public route
                log('Test 3: Navigate back to public route with locked session');
                navigateTo('/about');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const stillLockedOnPublic = await checkSessionLockState();
                if (stillLockedOnPublic) {
                    log('✅ Test 3 PASSED: Session still locked on public route, dialog should NOT show');
                } else {
                    log('❌ Test 3 FAILED: Session not locked on public route');
                    allTestsPassed = false;
                }
                
                // Test 4: Unlock session
                log('Test 4: Unlock session');
                await unlockSession();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const isUnlocked = await checkSessionLockState();
                if (!isUnlocked) {
                    log('✅ Test 4 PASSED: Session unlocked successfully');
                } else {
                    log('❌ Test 4 FAILED: Session still locked after unlock');
                    allTestsPassed = false;
                }
                
                // Final result
                if (allTestsPassed) {
                    log('🎉 ALL TESTS PASSED: Session lock route fix is working correctly!');
                    testResult.textContent = '✅ All Tests Passed';
                    testResult.className = 'status success';
                } else {
                    log('❌ SOME TESTS FAILED: Session lock route fix needs attention');
                    testResult.textContent = '❌ Some Tests Failed';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Test failed with error: ${error.message}`);
                testResult.textContent = '❌ Test Failed';
                testResult.className = 'status error';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Session Lock Route Fix Test initialized');
            updateStatus();
            
            // Listen for popstate events
            window.addEventListener('popstate', () => {
                updateStatus();
                log(`📍 Route changed via browser navigation to: ${window.location.pathname}`);
            });
        });
    </script>
</body>
</html>
