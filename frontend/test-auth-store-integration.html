<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Store Integration Test</title>
    <script type="module">
        // Simple test to verify auth store integration
        console.log('🚀 Testing Auth Store Integration...');
        
        // Test the auth service directly
        async function testAuthService() {
            try {
                // Import the auth service
                const { authService } = await import('./src/services/auth.ts');
                
                console.log('✅ Auth service imported successfully');
                
                // Test login
                console.log('🔐 Testing login...');
                const loginResponse = await authService.login({
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                console.log('✅ Login successful:', loginResponse);
                
                // Test session status
                console.log('📊 Testing session status...');
                const sessionStatus = await authService.getSessionStatus();
                
                console.log('✅ Session status:', sessionStatus);
                
                // Test activity update
                console.log('🔄 Testing activity update...');
                await authService.updateActivity();
                
                console.log('✅ Activity updated successfully');
                
                // Test session lock
                console.log('🔒 Testing session lock...');
                await authService.lockSession();
                
                console.log('✅ Session locked successfully');
                
                // Check session status after lock
                const lockedStatus = await authService.getSessionStatus();
                console.log('📊 Session status after lock:', lockedStatus);
                
                console.log('🎉 All auth service tests passed!');
                
            } catch (error) {
                console.error('❌ Auth service test failed:', error);
            }
        }
        
        // Test the auth store
        async function testAuthStore() {
            try {
                // Import the auth store
                const { useAuthStore } = await import('./src/stores/auth.ts');
                
                console.log('✅ Auth store imported successfully');
                
                const authStore = useAuthStore();
                
                console.log('📊 Initial auth state:', {
                    isAuthenticated: authStore.isAuthenticated,
                    isLocked: authStore.isLocked,
                    user: authStore.user
                });
                
                // Test login
                console.log('🔐 Testing store login...');
                const loginResult = await authStore.login({
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                console.log('✅ Store login successful:', loginResult);
                
                console.log('📊 Auth state after login:', {
                    isAuthenticated: authStore.isAuthenticated,
                    isLocked: authStore.isLocked,
                    user: authStore.user
                });
                
                // Test session lock check
                console.log('🔍 Testing session lock check...');
                await authStore.checkSessionLock();
                
                console.log('✅ Session lock check completed');
                
                // Test manual lock
                console.log('🔒 Testing manual session lock...');
                await authStore.lockSession();
                
                console.log('✅ Manual session lock completed');
                
                console.log('📊 Auth state after lock:', {
                    isAuthenticated: authStore.isAuthenticated,
                    isLocked: authStore.isLocked,
                    user: authStore.user
                });
                
                console.log('🎉 All auth store tests passed!');
                
            } catch (error) {
                console.error('❌ Auth store test failed:', error);
            }
        }
        
        // Run tests
        window.testAuthService = testAuthService;
        window.testAuthStore = testAuthStore;
        
        console.log('🧪 Test functions available:');
        console.log('  - testAuthService() - Test auth service directly');
        console.log('  - testAuthStore() - Test auth store integration');
        console.log('');
        console.log('Run either function in the console to test the integration.');
        
    </script>
</head>
<body>
    <h1>Auth Store Integration Test</h1>
    <p>Open the browser console and run:</p>
    <ul>
        <li><code>testAuthService()</code> - Test auth service directly</li>
        <li><code>testAuthStore()</code> - Test auth store integration</li>
    </ul>
    <p>Check the console for test results.</p>
</body>
</html>
