<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Redirect Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔄 Admin Login Redirect Test</h1>
    <p><strong>Fixed Issue:</strong> Removed enhanced auth setup check that was preventing redirect</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Current URL: <span id="currentUrl"></span></div>
        <div>Auth Status: <span id="authStatus">Not Authenticated</span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container">
        <h2>🧪 Test Admin Login Redirect</h2>
        <div>
            <button onclick="goToAdminLogin()">📄 Go to Admin Login</button>
            <button onclick="testLoginAPI()">🔑 Test Login API</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
        
        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <strong>Manual Test Steps:</strong>
            <ol>
                <li>Click "Go to Admin Login"</li>
                <li>Enter: <span class="highlight"><EMAIL></span></li>
                <li>Enter: <span class="highlight">admin123</span></li>
                <li>Click Login</li>
                <li>Should redirect to <span class="highlight">/admin</span></li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus() {
            document.getElementById('currentUrl').textContent = window.location.pathname;
        }

        function goToAdminLogin() {
            log('📄 Navigating to /admin/login');
            history.pushState({}, '', '/admin/login');
            updateStatus();
        }

        async function testLoginAPI() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🧪 TESTING ADMIN LOGIN API ===');
            testResult.textContent = 'Testing API...';
            testResult.className = 'status info';
            
            try {
                log('🔑 Attempting admin login...');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/vnd.hlenergy.v1+json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                log('✅ Login API successful');
                log(`Token received: ${result.data.token ? 'Yes' : 'No'}`);
                log(`User role: ${result.data.user?.role || 'Unknown'}`);
                
                // Check if user is admin
                if (result.data.user?.role === 'admin') {
                    log('✅ User has admin role');
                    testResult.textContent = '✅ API Works - Test Manual Login';
                    testResult.className = 'status success';
                    
                    document.getElementById('authStatus').textContent = 'Admin Authenticated';
                    document.getElementById('authStatus').style.color = '#28a745';
                } else {
                    log('❌ User does not have admin role');
                    testResult.textContent = '❌ Not Admin User';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Login API failed: ${error.message}`);
                testResult.textContent = '❌ API Failed';
                testResult.className = 'status error';
            }
            
            log('=== API test completed ===');
            log('📋 Now test the actual login form manually');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Admin Login Redirect Test initialized');
            log('🔧 Fixed: Removed checkEnhancedAuthSetup() call');
            log('🔧 Fixed: Direct redirect to /admin after login');
            log('📋 Ready to test admin login redirect');
            updateStatus();
        });
    </script>
</body>
</html>
