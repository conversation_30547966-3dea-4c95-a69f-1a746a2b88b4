<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page Unlock Redirect Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .test-section {
            border: 2px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .route-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔒 Login Page Unlock Redirect Test</h1>
    <p>This test verifies that unlocking session on login page doesn't redirect to dashboard.</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div class="route-info">
            <div>Current URL: <span id="currentUrl"></span></div>
            <div>Auth Status: <span id="authStatus"></span></div>
            <div>Session Lock State: <span id="lockState"></span></div>
            <div>Expected Behavior: <span id="expectedBehavior"></span></div>
        </div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container test-section">
        <h2>🧪 Login Page Unlock Test</h2>
        <div>
            <button onclick="testLoginPageUnlock()">🚀 Run Login Page Test</button>
            <button onclick="loginUser()">🔑 Login User</button>
            <button onclick="lockSession()">🔒 Lock Session</button>
            <button onclick="unlockSession()">🔓 Unlock Session</button>
        </div>
        <div>
            <button onclick="navigateTo('/login')">Go to Login (/login)</button>
            <button onclick="navigateTo('/admin')">Go to Admin (/admin)</button>
            <button onclick="navigateTo('/')">Go to Home (/)</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = null;

        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // API helper function
        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json'
                }
            };

            if (authToken) {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        // Update status display
        function updateStatus() {
            const currentPath = window.location.pathname;
            
            document.getElementById('currentUrl').textContent = currentPath;
            document.getElementById('authStatus').textContent = authToken ? 'AUTHENTICATED' : 'NOT AUTHENTICATED';
            document.getElementById('authStatus').style.color = authToken ? '#28a745' : '#dc3545';
            
            // Check session lock state
            checkSessionLockState();
            
            // Update expected behavior
            const isLoginPage = currentPath === '/login';
            const expectedBehavior = isLoginPage && authToken ? 
                'Should allow unlock without redirect' : 
                'Normal behavior';
            document.getElementById('expectedBehavior').textContent = expectedBehavior;
        }

        // Check current session lock state
        async function checkSessionLockState() {
            if (!authToken) {
                document.getElementById('lockState').textContent = 'N/A (Not authenticated)';
                document.getElementById('lockState').style.color = '#6c757d';
                return null;
            }

            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                
                document.getElementById('lockState').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockState').style.color = isLocked ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockState').textContent = 'ERROR';
                document.getElementById('lockState').style.color = '#6c757d';
                return null;
            }
        }

        // Navigate to a route
        function navigateTo(path) {
            log(`🔄 Navigating to: ${path}`);
            history.pushState({}, '', path);
            updateStatus();
            log(`📍 Navigation complete to: ${path}`);
        }

        // Login user
        async function loginUser() {
            log('🔑 Logging in user...');
            try {
                const response = await apiCall('POST', '/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                authToken = response.data.token;
                log('✅ Login successful');
                updateStatus();
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
            }
        }

        // Lock session
        async function lockSession() {
            if (!authToken) {
                log('❌ Cannot lock session - not authenticated');
                return;
            }

            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        // Unlock session
        async function unlockSession() {
            if (!authToken) {
                log('❌ Cannot unlock session - not authenticated');
                return;
            }

            log('🔓 Unlocking session...');
            try {
                await apiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    pin: '579973', // Use the updated PIN
                    deviceFingerprint: 'test-device'
                });
                log('✅ Session unlocked successfully');
                updateStatus();
                
                // Check if we're still on the same page
                const currentPath = window.location.pathname;
                log(`📍 Current page after unlock: ${currentPath}`);
                
                return true;
            } catch (error) {
                log(`❌ Failed to unlock session: ${error.message}`);
                return false;
            }
        }

        // Test the complete login page unlock scenario
        async function testLoginPageUnlock() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🚀 TESTING LOGIN PAGE UNLOCK SCENARIO ===');
            testResult.textContent = 'Testing in progress...';
            testResult.className = 'status warning';
            
            try {
                // Step 1: Login user
                log('Step 1: Login user');
                await loginUser();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Navigate to login page
                log('Step 2: Navigate to login page');
                navigateTo('/login');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 3: Lock session
                log('Step 3: Lock session');
                await lockSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 4: Unlock session on login page
                log('Step 4: Unlock session on login page');
                const unlockSuccess = await unlockSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 5: Check if we're still on login page
                const currentPath = window.location.pathname;
                log(`Step 5: Checking current page: ${currentPath}`);
                
                if (unlockSuccess && currentPath === '/login') {
                    log('✅ SUCCESS: Unlocked on login page without redirect!');
                    testResult.textContent = '✅ Test Passed - No Redirect';
                    testResult.className = 'status success';
                } else if (unlockSuccess && currentPath !== '/login') {
                    log('❌ FAILURE: Unlocked but redirected away from login page');
                    testResult.textContent = '❌ Test Failed - Redirected';
                    testResult.className = 'status error';
                } else {
                    log('❌ FAILURE: Unlock failed');
                    testResult.textContent = '❌ Test Failed - Unlock Failed';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Test failed with error: ${error.message}`);
                testResult.textContent = '❌ Test Failed';
                testResult.className = 'status error';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Login Page Unlock Redirect Test initialized');
            updateStatus();
            
            // Listen for popstate events
            window.addEventListener('popstate', () => {
                updateStatus();
                log(`📍 Route changed via browser navigation to: ${window.location.pathname}`);
            });
        });
    </script>
</body>
</html>
