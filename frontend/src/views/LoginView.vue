<template>
  <div class="min-h-screen bg-base-200 flex items-center justify-center">
    <div class="card w-96 bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title justify-center text-2xl mb-6">{{ $t('auth.login') }}</h2>
        
        <form @submit.prevent="handleLogin" class="space-y-4">
          <!-- Error Message -->
          <div v-if="errorMessage" class="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{ errorMessage }}</span>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('auth.email') }}</span>
            </label>
            <input
              type="email"
              v-model="loginForm.email"
              class="input input-bordered w-full"
              :placeholder="$t('auth.email')"
              :disabled="isLoading"
              required
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('auth.password') }}</span>
            </label>
            <input
              type="password"
              v-model="loginForm.password"
              class="input input-bordered w-full"
              :placeholder="$t('auth.password')"
              :disabled="isLoading"
              required
            />
          </div>

          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input
                type="checkbox"
                v-model="loginForm.rememberMe"
                class="checkbox checkbox-primary"
                :disabled="isLoading"
              />
              <span class="label-text ml-2">{{ $t('auth.remember_me') }}</span>
            </label>
          </div>

          <div class="form-control mt-6">
            <button
              type="submit"
              class="btn btn-primary w-full"
              :disabled="isLoading"
              :class="{ 'loading': isLoading }"
            >
              <span v-if="!isLoading">{{ $t('auth.login') }}</span>
              <span v-else>Signing in...</span>
            </button>
          </div>
        </form>
        
        <div class="divider">OR</div>
        
        <div class="text-center space-y-2">
          <p class="text-sm">
            {{ $t('auth.no_account') }}
            <RouterLink to="/register" class="link link-primary">{{ $t('auth.register') }}</RouterLink>
          </p>
          <p class="text-sm">
            <a href="#" class="link link-secondary">{{ $t('auth.forgot_password') }}</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// Note: Removed automatic logout for locked sessions
// The SessionLock component will handle locked sessions appropriately

const loginForm = reactive({
  email: '',
  password: '',
  rememberMe: false
})

const isLoading = ref(false)
const errorMessage = ref('')

const handleLogin = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    await authStore.login({
      email: loginForm.email,
      password: loginForm.password
    })

    // Redirect to dashboard or home
    const redirectTo = authStore.isAdmin ? '/admin' : '/dashboard'
    router.push(redirectTo)

  } catch (error: any) {
    errorMessage.value = error.message || 'Login failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}
</script>
