<template>
  <div v-if="shouldShowDialog" class="fixed inset-0 bg-black/50 backdrop-blur-md flex items-center justify-center z-50">
    <div class="glass-effect bg-base-100/90 backdrop-blur-md border border-white/20 rounded-lg shadow-2xl p-6 w-full max-w-md mx-4">
      <div class="text-center mb-6">
        <Icon :name="isOffline ? 'wifi' : 'lock'" size="lg" :class="isOffline ? 'text-info' : 'text-warning'" class="mx-auto mb-4" />
        <h2 class="text-2xl font-bold mb-2">
          {{ isOffline ? 'Offline Mode' : $t('auth.session_locked') }}
        </h2>
        <p class="text-base-content/70">
          {{ isOffline
            ? 'You are currently offline. Session lock is disabled while offline.'
            : $t('auth.session_locked_desc')
          }}
        </p>
      </div>

      <!-- Authentication Methods (hidden when offline) -->
      <div v-if="!isOffline" class="tabs tabs-boxed mb-6">
        <button 
          @click="authMethod = 'pin'"
          :class="['tab', { 'tab-active': authMethod === 'pin' }]"
        >
          <Icon name="hashtag" size="sm" class="mr-2" />
          PIN
        </button>
        <button 
          @click="authMethod = 'biometric'"
          :class="['tab', { 'tab-active': authMethod === 'biometric' }]"
          :disabled="!biometricAvailable"
        >
          <Icon name="fingerprint" size="sm" class="mr-2" />
          Biometric
        </button>
      </div>

      <!-- PIN Authentication (hidden when offline) -->
      <div v-if="authMethod === 'pin' && !isOffline" class="space-y-4">
        <div class="grid grid-cols-6 gap-2 max-w-sm mx-auto">
          <input
            v-for="(digit, index) in pinDigits"
            :key="index"
            v-model="pinDigits[index]"
            type="password"
            maxlength="1"
            class="input input-bordered text-center text-lg font-mono"
            :class="{ 'input-error': pinError }"
            @input="handlePinInput(index, $event)"
            @keydown="handlePinKeydown(index, $event)"
            :ref="el => pinInputs[index] = el"
          />
        </div>
        
        <div v-if="pinError" class="alert alert-error">
          <Icon name="exclamation-triangle" size="sm" />
          <span>{{ pinError }}</span>
        </div>

        <div class="text-center text-sm text-base-content/70 mb-4">
          Enter your 6-digit PIN. It will submit automatically when complete.
        </div>

        <!-- Optional manual submit button (only show if not all digits filled) -->
        <button
          v-if="pinDigits.some(d => !d)"
          @click="handlePinUnlock"
          class="btn btn-primary w-full"
          :disabled="isLoading || pinDigits.some(d => !d)"
        >
          <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
          <Icon v-if="!isLoading" name="unlock" size="sm" class="mr-2" />
          {{ isLoading ? $t('auth.verifying') : $t('auth.unlock_session') }}
        </button>

        <!-- Loading indicator when auto-submitting -->
        <div v-if="isLoading && !pinDigits.some(d => !d)" class="text-center">
          <span class="loading loading-spinner loading-md text-primary"></span>
          <p class="text-sm text-base-content/70 mt-2">{{ $t('auth.verifying') }}</p>
        </div>
      </div>

      <!-- Biometric Authentication (hidden when offline) -->
      <div v-if="authMethod === 'biometric' && !isOffline" class="space-y-4">
        <div v-if="biometricAvailable" class="text-center py-8">
          <Icon name="fingerprint" size="xl" class="text-primary mx-auto mb-4" />
          <p class="text-lg mb-4">{{ $t('auth.biometric_prompt') }}</p>
        </div>

        <div v-if="!biometricAvailable" class="text-center py-8">
          <Icon name="fingerprint" size="xl" class="text-warning mx-auto mb-4" />
          <p class="text-lg mb-4">Biometric authentication is not set up</p>
          <p class="text-sm text-base-content/70 mb-4">Set up biometric authentication to unlock with fingerprint or Face ID</p>
        </div>

        <button
          v-if="biometricAvailable"
          @click="handleBiometricUnlock"
          class="btn btn-primary w-full"
          :disabled="isLoading"
        >
          <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
          <Icon v-if="!isLoading" name="fingerprint" size="sm" class="mr-2" />
          {{ isLoading ? $t('auth.scanning') : $t('auth.authenticate') }}
        </button>

        <div v-if="!biometricAvailable" class="space-y-2">
          <button
            @click="handleBiometricSetup"
            class="btn btn-secondary w-full"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
            <Icon v-if="!isLoading" name="plus" size="sm" class="mr-2" />
            {{ isLoading ? 'Setting up...' : 'Set Up Biometric' }}
          </button>

          <!-- Reset option if there might be corrupted credentials -->
          <button
            v-if="localStorage.getItem('biometric_credential_id')"
            @click="handleBiometricReset"
            class="btn btn-outline btn-warning btn-sm w-full"
            :disabled="isLoading"
          >
            <Icon name="refresh" size="sm" class="mr-2" />
            Reset Biometric Setup
          </button>
        </div>
      </div>

      <!-- Logout Option (hidden when offline) -->
      <template v-if="!isOffline">
        <div class="divider">{{ $t('common.or') }}</div>
        <button
          @click="handleLogout"
          class="btn btn-outline btn-error w-full"
          :disabled="isLoading"
        >
          <Icon name="logout" size="sm" class="mr-2" />
          {{ $t('auth.logout') }}
        </button>
      </template>

      <!-- Offline Message -->
      <div v-if="isOffline" class="text-center mt-4">
        <div class="alert alert-info">
          <Icon name="info" size="sm" />
          <span>Session lock is disabled while offline. Your session will resume when you're back online.</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import Icon from '@/components/common/Icon.vue'

const authStore = useAuthStore()
const router = useRouter()

// Check if we're on any login page
const isOnLoginPage = computed(() => {
  const currentPath = router.currentRoute.value.path
  return currentPath === '/login' || currentPath === '/admin/login'
})

// Check if we're on a protected route that requires session lock
const isOnProtectedRoute = computed(() => {
  const currentPath = router.currentRoute.value.path
  const protectedRoutes = ['/admin', '/dashboard', '/profile', '/settings', '/account']
  return protectedRoutes.some(route => currentPath.startsWith(route))
})

// Determine if we should show the session lock dialog
const shouldShowDialog = computed(() => {
  const currentPath = router.currentRoute.value.path
  const isAuthenticated = authStore.isAuthenticated
  const isLocked = authStore.isLocked
  const hasToken = !!authStore.token
  const hasUser = !!authStore.user

  // Debug logging
  console.log('🔒 SessionLock shouldShowDialog check:', {
    currentPath,
    isOnLoginPage: isOnLoginPage.value,
    isOnProtectedRoute: isOnProtectedRoute.value,
    isAuthenticated,
    isLocked,
    hasToken,
    hasUser,
    shouldShow: (isOnLoginPage.value && isAuthenticated && isLocked) || (isOnProtectedRoute.value && isLocked)
  })

  // Don't show if auth store hasn't initialized yet
  if (!hasToken || !hasUser) {
    console.log('🔒 Auth store not ready yet, not showing dialog')
    return false
  }

  // Don't show on login page unless user is authenticated and locked
  if (isOnLoginPage.value) {
    const shouldShow = isAuthenticated && isLocked
    console.log(`🔒 Login page: shouldShow=${shouldShow}`)
    return shouldShow
  }

  // For other pages, only show on protected routes
  const shouldShow = isOnProtectedRoute.value && isLocked
  console.log(`🔒 Protected route: shouldShow=${shouldShow}`)
  return shouldShow
})

// State
const authMethod = ref<'pin' | 'biometric'>('pin')
const isLoading = ref(false)
const biometricAvailable = ref(false)
const isOffline = ref(!navigator.onLine)

// Debug logging
console.log('🔒 SessionLock component initialized with PIN method')

// Watch for auth method changes
watch(authMethod, (newMethod, oldMethod) => {
  console.log(`🔄 Auth method changed from ${oldMethod} to ${newMethod}`)
})

// Watch for auth state changes
watch(() => authStore.isAuthenticated, (newAuth, oldAuth) => {
  console.log(`🔄 Auth state changed: ${oldAuth} → ${newAuth}`)
})

watch(() => authStore.isLocked, (newLocked, oldLocked) => {
  console.log(`🔄 Lock state changed: ${oldLocked} → ${newLocked}`)
})

watch(() => router.currentRoute.value.path, (newPath, oldPath) => {
  console.log(`🔄 Route changed: ${oldPath} → ${newPath}`)
})

// Watch for session lock state changes to clear PIN
watch(() => authStore.isLocked, (isLocked) => {
  if (isLocked) {
    console.log('🔒 Session locked - clearing PIN inputs')
    clearPinInputs()
    clearPinError()
    // Reset to PIN method when session locks
    authMethod.value = 'pin'
    // Focus first input after clearing
    nextTick(() => {
      setTimeout(() => {
        pinInputs.value[0]?.focus()
      }, 100)
    })
  }
})

// PIN state
const pinDigits = ref(['', '', '', '', '', ''])
const pinInputs = ref<HTMLInputElement[]>([])
const pinError = ref('')

// Methods
const handlePinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  if (value && /^\d$/.test(value)) {
    pinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        pinInputs.value[index + 1]?.focus()
      })
    } else {
      // Last digit entered - auto-submit
      nextTick(() => {
        handlePinUnlock()
      })
    }
  } else {
    target.value = ''
    pinDigits.value[index] = ''
  }

  clearPinError()
}

const handlePinKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !pinDigits.value[index] && index > 0) {
    nextTick(() => {
      pinInputs.value[index - 1]?.focus()
    })
  }
}

const clearPinError = () => {
  pinError.value = ''
}

const clearPinInputs = () => {
  pinDigits.value = ['', '', '', '', '', '']
  console.log('🧹 PIN inputs cleared')
}

const handlePinUnlock = async () => {
  try {
    isLoading.value = true
    clearPinError()
    
    const pin = pinDigits.value.join('')
    if (pin.length !== 6) {
      pinError.value = 'Please enter a 6-digit PIN'
      return
    }
    
    const success = await authStore.unlockSession('pin', { pin })
    if (!success) {
      pinError.value = 'Invalid PIN. Please try again.'
      pinDigits.value = ['', '', '', '', '', '']
      nextTick(() => {
        pinInputs.value[0]?.focus()
      })
    }
    // Note: Removed automatic redirect - let router handle navigation
  } catch (error: any) {
    pinError.value = error.message || 'PIN verification failed'
  } finally {
    isLoading.value = false
  }
}

const handleBiometricUnlock = async () => {
  try {
    isLoading.value = true
    console.log('🔐 Starting real biometric authentication...')

    // Real biometric authentication using WebAuthn
    const biometricResult = await performBiometricAuth()

    if (biometricResult) {
      console.log('✅ Biometric authentication successful, unlocking session...')
      const success = await authStore.unlockSession('biometric')

      if (!success) {
        console.log('❌ Session unlock failed')
        alert('Session unlock failed. Please try again or use PIN.')
      }
      // Note: Removed automatic redirect - let router handle navigation
    } else {
      console.log('❌ Biometric authentication cancelled/failed')
      // Don't dismiss modal, just show message
      alert('Biometric authentication was cancelled. Please try again or use PIN.')
    }
  } catch (error) {
    console.error('❌ Biometric unlock error:', error)
    alert(`Biometric authentication error: ${error.message}. Please use PIN instead.`)
  } finally {
    isLoading.value = false
  }
}

// Real biometric authentication using WebAuthn
const performBiometricAuth = async (): Promise<boolean> => {
  try {
    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential) {
      throw new Error('WebAuthn is not supported in this browser')
    }

    // Check if platform authenticator is available
    const available = await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
    if (!available) {
      throw new Error('Platform authenticator (biometric) is not available')
    }

    // For session unlock, we can use a simpler approach
    // Try to use stored credentials first, but fall back to creating a temporary one
    const credentialId = localStorage.getItem('biometric_credential_id')

    if (credentialId) {
      // Try to use existing credentials
      try {
        return await authenticateWithStoredCredentials(credentialId)
      } catch (error) {
        console.log('❌ Stored credentials failed, trying temporary authentication...')
        // Fall through to temporary authentication
      }
    }

    // Use temporary biometric authentication for session unlock
    return await authenticateWithTemporaryCredentials()

  } catch (error: any) {
    console.error('❌ Biometric authentication failed:', error)

    // Handle specific error cases
    if (error.name === 'NotAllowedError') {
      throw new Error('Biometric authentication was cancelled or denied')
    } else if (error.name === 'InvalidStateError') {
      throw new Error('Biometric authenticator is not available')
    } else if (error.name === 'NotSupportedError') {
      throw new Error('Biometric authentication is not supported')
    } else {
      throw new Error(error.message || 'Biometric authentication failed')
    }
  }
}

// Authenticate with stored credentials
const authenticateWithStoredCredentials = async (credentialId: string): Promise<boolean> => {
  // Validate base64 string before decoding
  if (!/^[A-Za-z0-9+/]*={0,2}$/.test(credentialId)) {
    throw new Error('Invalid credential ID format')
  }

  const credentialIdBuffer = Uint8Array.from(atob(credentialId), c => c.charCodeAt(0))
  const challenge = new Uint8Array(32)
  window.crypto.getRandomValues(challenge)

  const authOptions: PublicKeyCredentialRequestOptions = {
    challenge: challenge,
    allowCredentials: [{
      id: credentialIdBuffer,
      type: 'public-key',
      transports: ['internal']
    }],
    userVerification: 'required',
    timeout: 60000
  }

  console.log('🔐 Requesting biometric authentication with stored credentials...')

  const credential = await navigator.credentials.get({
    publicKey: authOptions
  }) as PublicKeyCredential

  return !!credential
}

// Authenticate with temporary credentials (for session unlock)
const authenticateWithTemporaryCredentials = async (): Promise<boolean> => {
  const challenge = new Uint8Array(32)
  window.crypto.getRandomValues(challenge)

  // Create a temporary credential for authentication
  const userId = new TextEncoder().encode('temp_session_unlock')

  const creationOptions: PublicKeyCredentialCreationOptions = {
    challenge: challenge,
    rp: {
      name: 'HLenergy Session Unlock',
      id: window.location.hostname
    },
    user: {
      id: userId,
      name: 'session_unlock',
      displayName: 'Session Unlock'
    },
    pubKeyCredParams: [
      { alg: -7, type: 'public-key' }, // ES256
      { alg: -257, type: 'public-key' } // RS256
    ],
    authenticatorSelection: {
      authenticatorAttachment: 'platform',
      userVerification: 'required',
      requireResidentKey: false
    },
    timeout: 60000,
    attestation: 'none'
  }

  console.log('🔐 Requesting temporary biometric authentication for session unlock...')

  const credential = await navigator.credentials.create({
    publicKey: creationOptions
  }) as PublicKeyCredential

  return !!credential
}

const handleBiometricSetup = async () => {
  try {
    isLoading.value = true
    console.log('🔧 Setting up biometric authentication...')

    // Clear any existing corrupted credentials first
    clearBiometricCredentials()

    const success = await setupBiometricAuth()

    if (success) {
      alert('Biometric authentication set up successfully! You can now use it to unlock your session.')
      // Refresh biometric availability
      await checkBiometricAvailability()
    } else {
      alert('Biometric setup failed. Please try again.')
    }
  } catch (error: any) {
    console.error('❌ Biometric setup error:', error)
    alert(`Biometric setup error: ${error.message}`)
  } finally {
    isLoading.value = false
  }
}

// Clear biometric credentials (useful for corrupted data)
const clearBiometricCredentials = () => {
  try {
    localStorage.removeItem('biometric_credential_id')
    console.log('🧹 Cleared biometric credentials')
  } catch (error) {
    console.error('❌ Failed to clear biometric credentials:', error)
  }
}

// Handle biometric reset
const handleBiometricReset = async () => {
  try {
    const confirmed = confirm('This will clear your current biometric setup. You will need to set it up again. Continue?')
    if (!confirmed) return

    clearBiometricCredentials()
    await checkBiometricAvailability()
    alert('Biometric setup has been reset. You can now set it up again.')
  } catch (error: any) {
    console.error('❌ Biometric reset error:', error)
    alert(`Reset error: ${error.message}`)
  }
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

const checkBiometricAvailability = async () => {
  try {
    console.log('🔍 Checking biometric availability...')

    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential || !window.navigator.credentials) {
      console.log('❌ WebAuthn not supported')
      biometricAvailable.value = false
      return
    }

    // Check if platform authenticator is available
    const platformAvailable = await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()

    // For session unlock, we should allow biometric if platform supports it
    // We don't need pre-stored credentials for session unlock since the user is already authenticated
    // The biometric auth will use the browser's built-in authentication

    console.log(`🔐 Platform available: ${platformAvailable}`)
    biometricAvailable.value = platformAvailable
  } catch (error) {
    console.log('❌ Biometric availability check failed:', error)
    biometricAvailable.value = false
  }
}

// Function to set up biometric authentication (call this from admin settings)
const setupBiometricAuth = async (): Promise<boolean> => {
  try {
    console.log('🔧 Setting up biometric authentication...')

    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential) {
      throw new Error('WebAuthn is not supported in this browser')
    }

    // Check if platform authenticator is available
    const available = await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
    if (!available) {
      throw new Error('Platform authenticator (biometric) is not available on this device')
    }

    // Create a challenge for registration
    const challenge = new Uint8Array(32)
    window.crypto.getRandomValues(challenge)

    // User ID (in a real app, this would be the actual user ID)
    const userId = new TextEncoder().encode(authStore.user?.id?.toString() || 'user')
    const userName = authStore.user?.email || '<EMAIL>'

    // WebAuthn registration options
    const registrationOptions: PublicKeyCredentialCreationOptions = {
      challenge: challenge,
      rp: {
        name: 'HLenergy',
        id: window.location.hostname
      },
      user: {
        id: userId,
        name: userName,
        displayName: authStore.user?.name || 'User'
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' }, // ES256
        { alg: -257, type: 'public-key' } // RS256
      ],
      authenticatorSelection: {
        authenticatorAttachment: 'platform',
        userVerification: 'required',
        requireResidentKey: false
      },
      timeout: 60000,
      attestation: 'none'
    }

    console.log('🔐 Requesting biometric registration...')

    // Create credential
    const credential = await navigator.credentials.create({
      publicKey: registrationOptions
    }) as PublicKeyCredential

    if (credential) {
      // Store credential ID for future authentication with proper encoding
      try {
        const credentialIdArray = new Uint8Array(credential.rawId)
        const credentialId = btoa(String.fromCharCode(...credentialIdArray))

        // Validate the encoded string before storing
        if (!/^[A-Za-z0-9+/]*={0,2}$/.test(credentialId)) {
          throw new Error('Generated credential ID is not valid base64')
        }

        localStorage.setItem('biometric_credential_id', credentialId)
        console.log('✅ Biometric authentication set up successfully')
        console.log('🔑 Credential ID stored:', credentialId.substring(0, 20) + '...')
        return true
      } catch (error) {
        console.error('❌ Failed to store credential ID:', error)
        throw new Error('Failed to store biometric credentials')
      }
    } else {
      console.log('❌ No credential created')
      return false
    }

  } catch (error: any) {
    console.error('❌ Biometric setup failed:', error)

    // Handle specific error cases
    if (error.name === 'NotAllowedError') {
      throw new Error('Biometric setup was cancelled or denied')
    } else if (error.name === 'InvalidStateError') {
      throw new Error('Biometric authenticator is not available')
    } else if (error.name === 'NotSupportedError') {
      throw new Error('Biometric authentication is not supported on this device')
    } else {
      throw new Error(error.message || 'Biometric setup failed')
    }
  }
}

// Network status handlers
const updateNetworkStatus = () => {
  isOffline.value = !navigator.onLine
  console.log(`🌐 Network status changed: ${navigator.onLine ? 'online' : 'offline'}`)

  if (navigator.onLine && authStore.isLocked) {
    // Coming back online - check if session should still be locked
    authStore.checkSessionLock()
  }
}

// Lifecycle
onMounted(async () => {
  console.log('🚀 SessionLock mounted')

  // Set up network listeners
  window.addEventListener('online', updateNetworkStatus)
  window.addEventListener('offline', updateNetworkStatus)

  // Initial network status
  updateNetworkStatus()

  // Always start with PIN method
  authMethod.value = 'pin'
  console.log('📌 Auth method set to PIN')

  await checkBiometricAvailability()
  console.log(`🔐 Biometric available: ${biometricAvailable.value}`)

  // Focus first PIN input after a short delay to ensure DOM is ready
  nextTick(() => {
    setTimeout(() => {
      console.log('🎯 Focusing PIN input')
      pinInputs.value[0]?.focus()
    }, 100)
  })
})

onUnmounted(() => {
  // Clean up network listeners
  window.removeEventListener('online', updateNetworkStatus)
  window.removeEventListener('offline', updateNetworkStatus)
})
</script>

<style scoped>
/* Proper glass effect */
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-effect:hover::after {
  opacity: 1;
}

.glass-effect > div,
.glass-effect .text-center,
.glass-effect .tabs,
.glass-effect .space-y-4,
.glass-effect .divider,
.glass-effect button {
  position: relative;
  z-index: 3;
}

/* Enhanced hover effects */
.glass-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* Dark theme adjustments */
[data-theme="hlenergy-dark"] .glass-effect::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 100%
  );
}

[data-theme="hlenergy-dark"] .glass-effect:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}
</style>
