import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authService, type User, type LoginRequest, type RegisterRequest } from '@/services/auth'
import { biometricService } from '@/services/biometric'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Idle timeout state - secure session lock
  const isLocked = ref(false)
  const lastActivity = ref<Date>(new Date())
  const idleTimeout = ref<NodeJS.Timeout | null>(null)
  const IDLE_TIMEOUT_MINUTES = 15
  const sessionLockTime = ref<number | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value && !isLocked.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isStaff = computed(() => user.value?.role === 'staff' || isAdmin.value)
  const isEmailVerified = computed(() => user.value?.email_verified || false)
  const userInitials = computed(() => {
    if (!user.value) return ''
    const names = user.value.name.split(' ')
    return names.map(name => name.charAt(0).toUpperCase()).join('')
  })

  // Activity throttling to prevent excessive backend calls
  let activityUpdateTimeout: NodeJS.Timeout | null = null
  let sessionStatusCheckInterval: NodeJS.Timeout | null = null

  // Routes that require session lock protection
  const protectedRoutes = [
    '/admin',
    '/dashboard',
    '/profile',
    '/settings',
    '/account'
  ]

  // Check if current route requires session lock
  const isProtectedRoute = () => {
    if (typeof window === 'undefined') return false
    const currentPath = window.location.pathname
    return protectedRoutes.some(route => currentPath.startsWith(route))
  }

  // Idle timeout functions
  const updateActivity = async () => {
    // Only track activity on protected routes
    if (!isProtectedRoute()) {
      return
    }

    lastActivity.value = new Date()

    // Throttle backend activity updates to once every 30 seconds
    if (activityUpdateTimeout) {
      clearTimeout(activityUpdateTimeout)
    }

    activityUpdateTimeout = setTimeout(async () => {
      if (navigator.onLine && token.value && !isLocked.value && isProtectedRoute()) {
        try {
          await authService.updateActivity()
          console.log('🔄 Activity updated on backend (protected route)')
        } catch (error) {
          console.warn('Failed to update activity on backend:', error)
        }
      }
    }, 30000) // Update backend every 30 seconds

    resetIdleTimeout()
  }

  // Start periodic session status checks
  const startSessionStatusChecks = () => {
    if (sessionStatusCheckInterval) {
      clearInterval(sessionStatusCheckInterval)
    }

    // Check session status every 2 minutes - always check backend, but only enforce on protected routes
    sessionStatusCheckInterval = setInterval(async () => {
      if (token.value && user.value && navigator.onLine) {
        await checkSessionLock(true) // Force check to maintain session state
      }
    }, 2 * 60 * 1000) // 2 minutes
  }

  // Stop periodic session status checks
  const stopSessionStatusChecks = () => {
    if (sessionStatusCheckInterval) {
      clearInterval(sessionStatusCheckInterval)
      sessionStatusCheckInterval = null
    }
  }

  // Check session lock status from backend
  const checkSessionLock = async (forceCheck = false) => {
    if (!token.value || !user.value) {
      return // Not authenticated, no need to lock
    }

    // Only check session lock on protected routes, unless forced (for route transitions)
    if (!forceCheck && !isProtectedRoute()) {
      console.log('📍 Not on protected route - skipping session lock check')
      return
    }

    // Don't check session when offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - skipping session lock check')
      if (isProtectedRoute()) {
        resetIdleTimeout()
      }
      return
    }

    try {
      const sessionStatus = await authService.getSessionStatus()

      if (sessionStatus.isLocked && !isLocked.value) {
        console.log('🔒 Backend reports session is locked')
        isLocked.value = true
        sessionLockTime.value = sessionStatus.lockTime ? new Date(sessionStatus.lockTime).getTime() : Date.now()

        // Clear timeout since session is locked
        if (idleTimeout.value) {
          clearTimeout(idleTimeout.value)
          idleTimeout.value = null
        }

        // Session lock state is updated, component will handle display based on route
        console.log('🔒 Session locked - component will handle display based on route')
        return
      } else if (!sessionStatus.isLocked && isLocked.value) {
        console.log('🔓 Backend reports session is unlocked')
        isLocked.value = false
        sessionLockTime.value = null
      }

      // If session is not locked, reset the timeout
      if (!sessionStatus.isLocked) {
        resetIdleTimeout()
      }
    } catch (error) {
      console.error('Failed to check session status:', error)
      // On error, fall back to local timeout behavior but don't reset lock state
      if (!isLocked.value) {
        resetIdleTimeout()
      }
    }
  }

  const resetIdleTimeout = () => {
    if (idleTimeout.value) {
      clearTimeout(idleTimeout.value)
    }

    // Only set timeout if authenticated, not locked, online, and on protected route
    if (isAuthenticated.value && !isLocked.value && navigator.onLine && isProtectedRoute()) {
      idleTimeout.value = setTimeout(() => {
        // Double-check online status before locking
        if (navigator.onLine && isProtectedRoute()) {
          lockSession()
        } else {
          console.log('🌐 Offline or not on protected route during timeout - not locking session')
          // Reset timeout for when we come back online or navigate to protected route
          resetIdleTimeout()
        }
      }, IDLE_TIMEOUT_MINUTES * 60 * 1000)
    }
  }

  const lockSession = async () => {
    // Only lock on protected routes
    if (!isProtectedRoute()) {
      console.log('📍 Not on protected route - not locking session')
      return
    }

    // Don't lock if offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - not locking session')
      return
    }

    try {
      // Lock session on backend
      await authService.lockSession()

      // Update local state
      isLocked.value = true
      sessionLockTime.value = Date.now()

      // Stop session monitoring and timeouts when locked
      stopSessionStatusChecks()

      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }

      console.log('🔒 Session locked due to inactivity')

      // Session lock state is updated, component will handle display based on route
    } catch (error) {
      console.error('Failed to lock session on backend:', error)
      // Fall back to local lock if backend fails
      isLocked.value = true
      sessionLockTime.value = Date.now()

      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }

      window.dispatchEvent(new CustomEvent('session-locked'))
    }
  }

  const unlockSession = async (method: 'pin' | 'biometric', credentials?: any): Promise<boolean> => {
    try {
      const unlockData: any = {
        method,
        deviceFingerprint: await generateDeviceFingerprint()
      }

      if (method === 'pin') {
        if (!credentials?.pin) {
          throw new Error('PIN is required for PIN unlock')
        }
        unlockData.pin = credentials.pin
      } else if (method === 'biometric') {
        // For biometric unlock, we need to provide biometric data
        // In a real implementation, this would be the actual biometric signature
        unlockData.biometricData = credentials?.biometricData || { verified: true }
      }

      // Unlock session on backend
      await authService.unlockSession(unlockData)

      // Update local state
      isLocked.value = false
      sessionLockTime.value = null

      // Resume session monitoring (always monitor session state)
      startSessionStatusChecks()

      // Only start idle timeout if on protected route
      if (isProtectedRoute()) {
        updateActivity()
      }

      console.log('🔓 Session unlocked successfully')
      return true
    } catch (error: any) {
      console.error('❌ Session unlock failed:', error)
      return false
    }
  }

  // Verify PIN for session unlock (doesn't change tokens)
  const verifyPinForUnlock = async (pin: string) => {
    try {
      // Get current user ID from stored user data
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      console.log('🔐 Verifying PIN for session unlock...')

      // Call PIN verification endpoint (should not return new tokens)
      const response = await authService.verifyPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      console.log('📡 PIN verification response:', response)

      // Check if response exists and has success property
      if (!response || response.success !== true) {
        throw new Error('PIN verification failed')
      }

      console.log('✅ PIN verified successfully for session unlock')
      return true
    } catch (err: any) {
      console.error('❌ PIN verification failed:', err)
      // If it's a network error or API error, provide more context
      if (err.response?.status === 404) {
        throw new Error('PIN verification endpoint not found. Please check backend configuration.')
      } else if (err.response?.status === 401) {
        throw new Error('Invalid PIN. Please try again.')
      } else {
        throw new Error(err.message || 'PIN verification failed')
      }
    }
  }

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')

    // Clear session state
    isLocked.value = false
    sessionLockTime.value = null
  }

  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.login(credentials)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      // Start session status checks after successful login (always monitor session state)
      startSessionStatusChecks()

      // Only start idle timeout if on protected route
      if (isProtectedRoute()) {
        updateActivity()
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Login failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.register(userData)
      
      user.value = response.user
      setTokens(response.token, response.refreshToken)

      return response
    } catch (err: any) {
      setError(err.message || 'Registration failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true
      
      // Call logout API
      await authService.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // Clear local state regardless of API call result
      user.value = null
      clearTokens()
      clearError()
      isLoading.value = false

      // Clear idle timeout, session lock, and status checks
      isLocked.value = false
      sessionLockTime.value = null
      stopSessionStatusChecks()

      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }

      if (activityUpdateTimeout) {
        clearTimeout(activityUpdateTimeout)
        activityUpdateTimeout = null
      }
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authService.refreshToken(refreshToken.value)
      token.value = response.token
      localStorage.setItem('auth_token', response.token)

      return response.token
    } catch (err) {
      // Refresh failed, logout user
      await logout()
      throw err
    }
  }

  const fetchProfile = async () => {
    try {
      isLoading.value = true
      clearError()

      const profile = await authService.getProfile()
      user.value = profile

      return profile
    } catch (err: any) {
      setError(err.message || 'Failed to fetch profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      clearError()

      const updatedUser = await authService.updateProfile(profileData)
      user.value = updatedUser

      return updatedUser
    } catch (err: any) {
      setError(err.message || 'Failed to update profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.verifyEmail({ token })
      
      // Refresh user profile to get updated email_verified status
      if (user.value) {
        await fetchProfile()
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Email verification failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resendVerification = async () => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.resendEmailVerification()
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification email')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const requestPasswordReset = async (email: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.requestPasswordReset({ email })
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to request password reset')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (token: string, password: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.confirmPasswordReset({ token, password })
      return response
    } catch (err: any) {
      setError(err.message || 'Password reset failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.changePassword(currentPassword, newPassword)
      return response
    } catch (err: any) {
      setError(err.message || 'Password change failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Admin PIN authentication
  const loginWithPin = async (pin: string) => {
    try {
      isLoading.value = true
      clearError()

      // Get current user ID from stored user data or localStorage
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      const response = await authService.loginWithPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      user.value = response.user
      setTokens(response.accessToken, response.refreshToken)

      return { success: true, user: response.user, authMethod: 'pin' }
    } catch (err: any) {
      setError(err.message || 'PIN authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric authentication
  const loginWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric login
        const response = await authService.loginWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-login-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric login
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric authentication failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric session unlock (requires existing session)
  const unlockWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if user session exists
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric unlock
        const response = await authService.unlockWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-unlock-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric unlock
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        user.value = userData
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric unlock failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric unlock failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Setup biometric authentication
  const setupBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available on this device')
      }

      const credential = await biometricService.register()

      // Register the credential with the backend
      const response = await authService.registerBiometric({
        credentialId: credential.id,
        publicKey: btoa(JSON.stringify(credential.response)), // Convert to base64
        deviceType: 'platform',
        deviceName: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
        transport: ['internal'],
        deviceFingerprint: await generateDeviceFingerprint()
      })

      if (response.success) {
        localStorage.setItem('biometric_credential_id', credential.id)
        return { success: true, credential, credentialId: response.credentialId }
      } else {
        throw new Error(response.message || 'Failed to register biometric credential')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric setup failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Check biometric capabilities
  const getBiometricCapabilities = async () => {
    try {
      return await biometricService.getCapabilities()
    } catch (err: any) {
      console.warn('Failed to get biometric capabilities:', err)
      return {
        supported: false,
        available: false,
        authenticators: [],
        hasCredentials: false
      }
    }
  }

  // Generate device fingerprint for security
  const generateDeviceFingerprint = async () => {
    // Generate a simple device fingerprint
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('Device fingerprint', 10, 10)

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // Simple hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36)
  }

  // Initialize auth state on app start
  const initializeAuth = async () => {
    if (token.value && !user.value) {
      try {
        await fetchProfile()
      } catch (err) {
        // Token is invalid, clear it
        clearTokens()
      }
    }

    // Check session status from backend and start monitoring
    if (token.value && user.value) {
      await checkSessionLock(true) // Force check on initialization
      startSessionStatusChecks() // Always start monitoring to maintain session state
    }

    // Set up network status listeners
    setupNetworkListeners()

    // Set up route change listeners for session lock
    setupRouteListeners()
  }

  const setupNetworkListeners = () => {
    // Handle coming back online
    const handleOnline = () => {
      console.log('🌐 Network back online - resuming session timeout')
      if (isAuthenticated.value && !isLocked.value) {
        resetIdleTimeout()
      }
    }

    // Handle going offline
    const handleOffline = () => {
      console.log('🌐 Network offline - pausing session timeout')
      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }
    }

    // Add event listeners
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden - user might be trying to bypass lock
        console.log('📱 Page hidden - updating last activity')
        updateActivity()
      } else {
        // Page is visible again - check if lock is required
        console.log('📱 Page visible - checking session lock status')
        if (isAuthenticated.value) {
          checkSessionLock()
        }
      }
    }

    // Add visibility listener
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Store cleanup function
    window.authNetworkCleanup = () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }

  // Set up route change listeners
  const setupRouteListeners = () => {
    // Listen for route changes to manage session lock
    const handleRouteChange = async () => {
      if (isAuthenticated.value) {
        if (isProtectedRoute()) {
          // Entering protected route - ALWAYS check backend session status first
          console.log('📍 Entered protected route - checking session status')
          await checkSessionLock(true) // Force check even if not currently on protected route

          // Only start monitoring if session is not locked
          if (!isLocked.value) {
            console.log('📍 Session unlocked - starting session monitoring')
            resetIdleTimeout()
            startSessionStatusChecks()
          } else {
            console.log('📍 Session locked - component will handle display')
            // Session lock state is updated, component will handle display based on route
          }
        } else {
          // Leaving protected route - stop session monitoring but preserve lock state
          console.log('📍 Left protected route - stopping session monitoring')
          if (idleTimeout.value) {
            clearTimeout(idleTimeout.value)
            idleTimeout.value = null
          }
          stopSessionStatusChecks()
          // Note: We don't clear isLocked.value here - lock state persists
        }
      }
    }

    // Listen for popstate (back/forward navigation)
    window.addEventListener('popstate', handleRouteChange)

    // Listen for pushstate/replacestate (programmatic navigation)
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState

    history.pushState = function(...args) {
      originalPushState.apply(history, args)
      setTimeout(handleRouteChange, 0)
    }

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args)
      setTimeout(handleRouteChange, 0)
    }
  }

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    isAdmin,
    isStaff,
    isEmailVerified,
    userInitials,

    // Idle timeout state
    isLocked: readonly(isLocked),
    lastActivity: readonly(lastActivity),

    // Actions
    setError,
    clearError,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchProfile,
    updateProfile,
    verifyEmail,
    resendVerification,
    requestPasswordReset,
    resetPassword,
    changePassword,
    loginWithPin,
    loginWithBiometric,
    unlockWithBiometric,
    setupBiometric,
    getBiometricCapabilities,
    initializeAuth,
    // Idle timeout actions
    updateActivity,
    lockSession,
    unlockSession,
    verifyPinForUnlock,
    checkSessionLock,
  }
})
