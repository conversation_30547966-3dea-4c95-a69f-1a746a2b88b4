<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Session Lock Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        .locked { background: #fff3cd; color: #856404; }
        .unlocked { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .auth-section {
            border: 2px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .session-section {
            border: 2px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .test-section {
            border: 2px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔒 Backend Session Lock Integration Test</h1>
    <p>This page tests the new backend-based session lock system with real API calls.</p>

    <div class="container auth-section">
        <h2>🔐 Authentication</h2>
        <div>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Login</button>
            <button onclick="testLogout()">Logout</button>
        </div>
        <div id="authStatus" class="status">Not authenticated</div>
        <div id="tokenStatus" class="status">No token</div>
    </div>

    <div class="container session-section">
        <h2>🔄 Session Management</h2>
        <div>
            <button onclick="checkSessionStatus()">Check Session Status</button>
            <button onclick="updateActivity()">Update Activity</button>
            <button onclick="lockSession()">Lock Session</button>
            <button onclick="unlockSession()">Unlock Session (PIN)</button>
        </div>
        <div id="sessionStatus" class="status">Unknown</div>
        <div id="lockStatus" class="status">Unknown</div>
        <div id="activityStatus" class="status">Unknown</div>
    </div>

    <div class="container test-section">
        <h2>🧪 Security Tests</h2>
        <div>
            <button onclick="testBypassPrevention()">Test Bypass Prevention</button>
            <button onclick="testAutoLock()">Test Auto-Lock (15min simulation)</button>
            <button onclick="testOfflineHandling()">Test Offline Handling</button>
            <button onclick="clearAllData()">Clear All Data</button>
        </div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = null;
        let user = null;

        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // API helper function
        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json'
                }
            };

            if (authToken) {
                options.headers.Authorization = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        // Update UI status
        function updateAuthStatus() {
            const authStatus = document.getElementById('authStatus');
            const tokenStatus = document.getElementById('tokenStatus');
            
            if (authToken && user) {
                authStatus.textContent = `✅ Authenticated as ${user.name} (${user.role})`;
                authStatus.className = 'status online';
                tokenStatus.textContent = `🔑 Token: ${authToken.substring(0, 20)}...`;
                tokenStatus.className = 'status online';
            } else {
                authStatus.textContent = '❌ Not authenticated';
                authStatus.className = 'status offline';
                tokenStatus.textContent = '🚫 No token';
                tokenStatus.className = 'status offline';
            }
        }

        function updateSessionStatus(sessionData) {
            const sessionStatus = document.getElementById('sessionStatus');
            const lockStatus = document.getElementById('lockStatus');
            const activityStatus = document.getElementById('activityStatus');

            if (sessionData) {
                sessionStatus.textContent = `📊 Session active since ${new Date(sessionData.sessionStartTime).toLocaleTimeString()}`;
                sessionStatus.className = 'status online';

                if (sessionData.isLocked) {
                    lockStatus.textContent = `🔒 LOCKED since ${new Date(sessionData.lockTime).toLocaleTimeString()}`;
                    lockStatus.className = 'status locked';
                } else {
                    lockStatus.textContent = '🔓 UNLOCKED';
                    lockStatus.className = 'status unlocked';
                }

                activityStatus.textContent = `🔄 Last activity: ${new Date(sessionData.lastActivity).toLocaleTimeString()}`;
                activityStatus.className = 'status online';
            } else {
                sessionStatus.textContent = '❌ No session data';
                sessionStatus.className = 'status offline';
                lockStatus.textContent = '❓ Unknown lock status';
                lockStatus.className = 'status offline';
                activityStatus.textContent = '❓ Unknown activity';
                activityStatus.className = 'status offline';
            }
        }

        // Test functions
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            log('🔐 Testing login...');
            try {
                const response = await apiCall('POST', '/auth/login', { email, password });
                authToken = response.data.token;
                user = response.data.user;
                
                log(`✅ Login successful: ${user.name} (${user.role})`);
                log(`🔑 Token: ${authToken.substring(0, 30)}...`);
                
                updateAuthStatus();
                
                // Check initial session status
                await checkSessionStatus();
                
                return true;
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
                return false;
            }
        }

        async function testLogout() {
            log('🚪 Testing logout...');
            try {
                if (authToken) {
                    await apiCall('POST', '/auth/logout');
                }
                
                authToken = null;
                user = null;
                
                log('✅ Logout successful');
                updateAuthStatus();
                updateSessionStatus(null);
                
                return true;
            } catch (error) {
                log(`❌ Logout failed: ${error.message}`);
                return false;
            }
        }

        async function checkSessionStatus() {
            if (!authToken) {
                log('❌ Cannot check session status - not authenticated');
                return;
            }

            log('📊 Checking session status...');
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const sessionData = response.data;
                
                log(`✅ Session status retrieved:`);
                log(`   - Locked: ${sessionData.isLocked}`);
                log(`   - Lock Time: ${sessionData.lockTime || 'N/A'}`);
                log(`   - Last Activity: ${sessionData.lastActivity}`);
                log(`   - Session Start: ${sessionData.sessionStartTime}`);
                
                updateSessionStatus(sessionData);
                return sessionData;
            } catch (error) {
                log(`❌ Session status check failed: ${error.message}`);
                return null;
            }
        }

        async function updateActivity() {
            if (!authToken) {
                log('❌ Cannot update activity - not authenticated');
                return;
            }

            log('🔄 Updating activity...');
            try {
                await apiCall('POST', '/auth/session/activity');
                log('✅ Activity updated successfully');
                
                // Refresh session status
                setTimeout(() => checkSessionStatus(), 1000);
                
                return true;
            } catch (error) {
                log(`❌ Activity update failed: ${error.message}`);
                return false;
            }
        }

        async function lockSession() {
            if (!authToken) {
                log('❌ Cannot lock session - not authenticated');
                return;
            }

            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                
                // Refresh session status
                setTimeout(() => checkSessionStatus(), 1000);
                
                return true;
            } catch (error) {
                log(`❌ Session lock failed: ${error.message}`);
                return false;
            }
        }

        async function unlockSession() {
            if (!authToken) {
                log('❌ Cannot unlock session - not authenticated');
                return;
            }

            log('🔓 Unlocking session with PIN...');
            try {
                await apiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    pin: '123456',
                    deviceFingerprint: 'test-device-' + Date.now()
                });
                log('✅ Session unlocked successfully');
                
                // Refresh session status
                setTimeout(() => checkSessionStatus(), 1000);
                
                return true;
            } catch (error) {
                log(`❌ Session unlock failed: ${error.message}`);
                log('ℹ️  This may fail if no PIN is configured for the user');
                return false;
            }
        }

        async function testBypassPrevention() {
            log('=== Testing Bypass Prevention ===');
            log('1. Current session state is stored on the backend');
            log('2. Attempting to clear localStorage...');
            
            localStorage.clear();
            log('✅ localStorage cleared');
            
            log('3. Checking if session status is still enforced by backend...');
            const sessionData = await checkSessionStatus();
            
            if (sessionData) {
                log('✅ SUCCESS: Backend still enforces session state!');
                log('   - Session lock cannot be bypassed by clearing localStorage');
            } else {
                log('❌ FAIL: Session state was lost');
            }
        }

        async function testAutoLock() {
            log('=== Testing Auto-Lock Simulation ===');
            log('ℹ️  This would normally require waiting 15 minutes');
            log('ℹ️  In a real scenario, the backend would auto-lock inactive sessions');
            log('✅ Auto-lock is handled entirely by the backend based on last_activity timestamp');
        }

        async function testOfflineHandling() {
            log('=== Testing Offline Handling ===');
            log('ℹ️  The frontend gracefully handles offline scenarios');
            log('ℹ️  Session lock state is preserved on the backend');
            log('✅ When back online, session status is re-synchronized with backend');
        }

        function clearAllData() {
            log('🧹 Clearing all data...');
            localStorage.clear();
            sessionStorage.clear();
            authToken = null;
            user = null;
            updateAuthStatus();
            updateSessionStatus(null);
            log('✅ All data cleared');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Backend Session Lock Integration Test initialized');
            log('ℹ️  This page tests the secure backend-based session lock system');
            updateAuthStatus();
        });
    </script>
</body>
</html>
