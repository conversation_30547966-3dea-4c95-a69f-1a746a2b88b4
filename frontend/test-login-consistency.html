<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page Consistency Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .test-section {
            border: 2px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .login-type {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔄 Login Page Consistency Test</h1>
    <p>This test verifies that both normal login and admin login handle session locks consistently.</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Auth Status: <span id="authStatus"></span></div>
        <div>Session Lock: <span id="lockStatus"></span></div>
        <div>Current Page: <span id="currentPage"></span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container test-section">
        <h2>🧪 Consistency Test</h2>
        <div>
            <button onclick="runConsistencyTest()">🚀 Run Full Consistency Test</button>
            <button onclick="loginUser()">🔑 Login User</button>
            <button onclick="lockSession()">🔒 Lock Session</button>
            <button onclick="unlockSession()">🔓 Unlock Session</button>
        </div>
        
        <div class="comparison">
            <div class="login-type">
                <h3>Normal Login (/login)</h3>
                <button onclick="testNormalLogin()">Test Normal Login</button>
                <div id="normalResult" class="status info">Ready to test</div>
            </div>
            <div class="login-type">
                <h3>Admin Login (/admin-login)</h3>
                <button onclick="testAdminLogin()">Test Admin Login</button>
                <div id="adminResult" class="status info">Ready to test</div>
            </div>
        </div>
        
        <div id="overallResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json'
                }
            };

            if (authToken) {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        function updateStatus() {
            document.getElementById('authStatus').textContent = authToken ? 'AUTHENTICATED' : 'NOT AUTHENTICATED';
            document.getElementById('authStatus').style.color = authToken ? '#28a745' : '#dc3545';
            document.getElementById('currentPage').textContent = window.location.pathname;
            
            if (authToken) {
                checkSessionStatus();
            } else {
                document.getElementById('lockStatus').textContent = 'N/A';
                document.getElementById('lockStatus').style.color = '#6c757d';
            }
        }

        async function checkSessionStatus() {
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                
                document.getElementById('lockStatus').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockStatus').style.color = isLocked ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockStatus').textContent = 'ERROR';
                document.getElementById('lockStatus').style.color = '#6c757d';
                return null;
            }
        }

        async function loginUser() {
            log('🔑 Logging in user...');
            try {
                const response = await apiCall('POST', '/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                authToken = response.data.token;
                log('✅ Login successful');
                updateStatus();
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
            }
        }

        async function lockSession() {
            if (!authToken) {
                log('❌ Cannot lock session - not authenticated');
                return;
            }

            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        async function unlockSession() {
            if (!authToken) {
                log('❌ Cannot unlock session - not authenticated');
                return;
            }

            log('🔓 Unlocking session...');
            try {
                await apiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    pin: '579973',
                    deviceFingerprint: 'test-device'
                });
                log('✅ Session unlocked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to unlock session: ${error.message}`);
            }
        }

        async function testNormalLogin() {
            log('=== Testing Normal Login Page ===');
            const resultElement = document.getElementById('normalResult');
            
            try {
                resultElement.textContent = 'Testing...';
                resultElement.className = 'status warning';
                
                // Navigate to normal login
                log('📄 Navigating to /login');
                history.pushState({}, '', '/login');
                
                // Wait a moment for any automatic behaviors
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Check if we're still authenticated and locked
                const isLocked = await checkSessionStatus();
                const stillAuthenticated = authToken !== null;
                
                if (stillAuthenticated && isLocked) {
                    log('✅ Normal login: Session remains locked, no automatic logout');
                    resultElement.textContent = '✅ Correct Behavior';
                    resultElement.className = 'status success';
                    return true;
                } else if (!stillAuthenticated) {
                    log('❌ Normal login: User was automatically logged out');
                    resultElement.textContent = '❌ Auto Logout Detected';
                    resultElement.className = 'status error';
                    return false;
                } else {
                    log('⚠️ Normal login: Session not locked');
                    resultElement.textContent = '⚠️ Session Not Locked';
                    resultElement.className = 'status warning';
                    return false;
                }
                
            } catch (error) {
                log(`❌ Normal login test failed: ${error.message}`);
                resultElement.textContent = '❌ Test Failed';
                resultElement.className = 'status error';
                return false;
            }
        }

        async function testAdminLogin() {
            log('=== Testing Admin Login Page ===');
            const resultElement = document.getElementById('adminResult');
            
            try {
                resultElement.textContent = 'Testing...';
                resultElement.className = 'status warning';
                
                // Navigate to admin login
                log('📄 Navigating to /admin-login');
                history.pushState({}, '', '/admin-login');
                
                // Wait a moment for any automatic behaviors
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Check if we're still authenticated and locked
                const isLocked = await checkSessionStatus();
                const stillAuthenticated = authToken !== null;
                
                if (stillAuthenticated && isLocked) {
                    log('✅ Admin login: Session remains locked, no automatic logout');
                    resultElement.textContent = '✅ Correct Behavior';
                    resultElement.className = 'status success';
                    return true;
                } else if (!stillAuthenticated) {
                    log('❌ Admin login: User was automatically logged out');
                    resultElement.textContent = '❌ Auto Logout Detected';
                    resultElement.className = 'status error';
                    return false;
                } else {
                    log('⚠️ Admin login: Session not locked');
                    resultElement.textContent = '⚠️ Session Not Locked';
                    resultElement.className = 'status warning';
                    return false;
                }
                
            } catch (error) {
                log(`❌ Admin login test failed: ${error.message}`);
                resultElement.textContent = '❌ Test Failed';
                resultElement.className = 'status error';
                return false;
            }
        }

        async function runConsistencyTest() {
            const overallResult = document.getElementById('overallResult');
            
            log('=== 🚀 RUNNING FULL CONSISTENCY TEST ===');
            overallResult.textContent = 'Testing in progress...';
            overallResult.className = 'status warning';
            
            try {
                // Step 1: Login user
                log('Step 1: Login user');
                await loginUser();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Lock session
                log('Step 2: Lock session');
                await lockSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 3: Test both login pages
                log('Step 3: Test both login pages');
                const normalResult = await testNormalLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const adminResult = await testAdminLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 4: Evaluate results
                if (normalResult && adminResult) {
                    log('🎉 SUCCESS: Both login pages behave consistently!');
                    overallResult.textContent = '✅ Consistency Test Passed';
                    overallResult.className = 'status success';
                } else {
                    log('❌ FAILURE: Login pages behave inconsistently');
                    overallResult.textContent = '❌ Consistency Test Failed';
                    overallResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Consistency test failed: ${error.message}`);
                overallResult.textContent = '❌ Test Failed';
                overallResult.className = 'status error';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Login Page Consistency Test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
