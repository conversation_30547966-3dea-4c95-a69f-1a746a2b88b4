<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔒 Admin Login Lock Test</h1>
    <p>Testing the specific issue: locked session + refresh dashboard → admin/login → login does nothing</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Current URL: <span id="currentUrl"></span></div>
        <div>Auth Status: <span id="authStatus"></span></div>
        <div>Session Lock: <span id="lockStatus"></span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container">
        <h2>🧪 Reproduce Issue</h2>
        <div>
            <button onclick="reproduceIssue()">🚀 Reproduce Full Issue</button>
            <button onclick="loginUser()">🔑 Login User</button>
            <button onclick="lockSession()">🔒 Lock Session</button>
            <button onclick="goToAdminLogin()">📄 Go to Admin Login</button>
            <button onclick="testAdminLogin()">🔓 Test Admin Login</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json'
                }
            };

            if (authToken) {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        function updateStatus() {
            document.getElementById('currentUrl').textContent = window.location.pathname;
            document.getElementById('authStatus').textContent = authToken ? 'AUTHENTICATED' : 'NOT AUTHENTICATED';
            document.getElementById('authStatus').style.color = authToken ? '#28a745' : '#dc3545';
            
            if (authToken) {
                checkSessionStatus();
            } else {
                document.getElementById('lockStatus').textContent = 'N/A';
                document.getElementById('lockStatus').style.color = '#6c757d';
            }
        }

        async function checkSessionStatus() {
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                
                document.getElementById('lockStatus').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockStatus').style.color = isLocked ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockStatus').textContent = 'ERROR';
                document.getElementById('lockStatus').style.color = '#6c757d';
                return null;
            }
        }

        async function loginUser() {
            log('🔑 Logging in user...');
            try {
                const response = await apiCall('POST', '/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                authToken = response.data.token;
                log('✅ Login successful');
                updateStatus();
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
            }
        }

        async function lockSession() {
            if (!authToken) {
                log('❌ Cannot lock session - not authenticated');
                return;
            }

            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        function goToAdminLogin() {
            log('📄 Navigating to /admin/login');
            history.pushState({}, '', '/admin/login');
            updateStatus();
        }

        async function testAdminLogin() {
            if (!authToken) {
                log('❌ Cannot test admin login - not authenticated');
                return;
            }

            log('🔓 Testing admin login with PIN...');
            try {
                const response = await apiCall('POST', '/auth/admin/login', {
                    method: 'pin',
                    pin: '579973',
                    deviceFingerprint: 'test-device'
                });
                
                log('✅ Admin login successful');
                log('Response:', JSON.stringify(response, null, 2));
                updateStatus();
            } catch (error) {
                log(`❌ Admin login failed: ${error.message}`);
            }
        }

        async function reproduceIssue() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🚀 REPRODUCING ADMIN LOGIN LOCK ISSUE ===');
            testResult.textContent = 'Reproducing issue...';
            testResult.className = 'status warning';
            
            try {
                // Step 1: Login user
                log('Step 1: Login user');
                await loginUser();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Simulate being on dashboard
                log('Step 2: Simulate being on /admin dashboard');
                history.pushState({}, '', '/admin');
                updateStatus();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 3: Lock session
                log('Step 3: Lock session (simulating idle timeout)');
                await lockSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 4: Simulate refresh (redirect to admin/login)
                log('Step 4: Simulate refresh → redirect to /admin/login');
                goToAdminLogin();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Step 5: Check if SessionLock modal should appear
                const isLocked = await checkSessionStatus();
                log(`Step 5: Session lock status: ${isLocked ? 'LOCKED' : 'UNLOCKED'}`);
                
                if (isLocked) {
                    log('✅ Session is locked - SessionLock modal should appear');
                    log('🔍 Check browser console for SessionLock debug logs');
                    
                    // Step 6: Try admin login
                    log('Step 6: Attempting admin login...');
                    await testAdminLogin();
                    
                    testResult.textContent = '✅ Issue Reproduced - Check Console';
                    testResult.className = 'status success';
                } else {
                    log('❌ Session not locked - cannot reproduce issue');
                    testResult.textContent = '❌ Cannot Reproduce';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Issue reproduction failed: ${error.message}`);
                testResult.textContent = '❌ Reproduction Failed';
                testResult.className = 'status error';
            }
            
            log('=== Issue reproduction completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Admin Login Lock Test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
