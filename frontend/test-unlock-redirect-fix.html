<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unlock Redirect Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔓 Unlock Redirect Fix Test</h1>
    <p>Testing that unlocking on login page doesn't redirect to dashboard.</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Current URL: <span id="currentUrl"></span></div>
        <div>Auth Status: <span id="authStatus"></span></div>
        <div>Session Lock: <span id="lockStatus"></span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container">
        <h2>🧪 Test Actions</h2>
        <button onclick="runFullTest()">🚀 Run Full Test</button>
        <button onclick="loginUser()">🔑 Login</button>
        <button onclick="lockSession()">🔒 Lock Session</button>
        <button onclick="unlockSession()">🔓 Unlock Session</button>
        <button onclick="goToLogin()">📄 Go to Login Page</button>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json'
                }
            };

            if (authToken) {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        function updateStatus() {
            document.getElementById('currentUrl').textContent = window.location.pathname;
            document.getElementById('authStatus').textContent = authToken ? 'AUTHENTICATED' : 'NOT AUTHENTICATED';
            document.getElementById('authStatus').style.color = authToken ? '#28a745' : '#dc3545';
            
            if (authToken) {
                checkSessionStatus();
            } else {
                document.getElementById('lockStatus').textContent = 'N/A';
                document.getElementById('lockStatus').style.color = '#6c757d';
            }
        }

        async function checkSessionStatus() {
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                
                document.getElementById('lockStatus').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockStatus').style.color = isLocked ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockStatus').textContent = 'ERROR';
                document.getElementById('lockStatus').style.color = '#6c757d';
                return null;
            }
        }

        function goToLogin() {
            log('📄 Navigating to login page');
            history.pushState({}, '', '/login');
            updateStatus();
        }

        async function loginUser() {
            log('🔑 Logging in user...');
            try {
                const response = await apiCall('POST', '/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                authToken = response.data.token;
                log('✅ Login successful');
                updateStatus();
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
            }
        }

        async function lockSession() {
            if (!authToken) {
                log('❌ Cannot lock session - not authenticated');
                return;
            }

            log('🔒 Locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateStatus();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        async function unlockSession() {
            if (!authToken) {
                log('❌ Cannot unlock session - not authenticated');
                return;
            }

            const currentPath = window.location.pathname;
            log(`🔓 Unlocking session on page: ${currentPath}`);
            
            try {
                await apiCall('POST', '/auth/session/unlock', {
                    method: 'pin',
                    pin: '579973',
                    deviceFingerprint: 'test-device'
                });
                
                log('✅ Session unlocked successfully');
                
                // Wait a moment and check if we're still on the same page
                setTimeout(() => {
                    const newPath = window.location.pathname;
                    log(`📍 Page after unlock: ${newPath}`);
                    
                    if (currentPath === newPath) {
                        log('✅ SUCCESS: Stayed on same page after unlock');
                    } else {
                        log(`❌ FAILURE: Redirected from ${currentPath} to ${newPath}`);
                    }
                    
                    updateStatus();
                }, 1500);
                
            } catch (error) {
                log(`❌ Failed to unlock session: ${error.message}`);
            }
        }

        async function runFullTest() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🚀 RUNNING FULL UNLOCK REDIRECT TEST ===');
            testResult.textContent = 'Testing in progress...';
            testResult.className = 'status warning';
            
            try {
                // Step 1: Login
                log('Step 1: Login user');
                await loginUser();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Go to login page
                log('Step 2: Navigate to login page');
                goToLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 3: Lock session
                log('Step 3: Lock session');
                await lockSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 4: Unlock session and check for redirect
                log('Step 4: Unlock session and check for redirect');
                const beforePath = window.location.pathname;
                
                await unlockSession();
                
                // Wait for potential redirect
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const afterPath = window.location.pathname;
                
                if (beforePath === afterPath && afterPath === '/login') {
                    log('🎉 TEST PASSED: No redirect after unlock on login page');
                    testResult.textContent = '✅ Test Passed - No Redirect';
                    testResult.className = 'status success';
                } else {
                    log(`❌ TEST FAILED: Redirected from ${beforePath} to ${afterPath}`);
                    testResult.textContent = '❌ Test Failed - Redirected';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Test failed with error: ${error.message}`);
                testResult.textContent = '❌ Test Failed';
                testResult.className = 'status error';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Unlock Redirect Fix Test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
