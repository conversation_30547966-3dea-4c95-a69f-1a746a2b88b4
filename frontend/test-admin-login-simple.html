<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔑 Simple Admin Login Test</h1>
    <p>Testing: Admin login shows only password option and redirects to /admin on success</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Current URL: <span id="currentUrl"></span></div>
        <div>Test Status: <span id="testStatus">Ready</span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container">
        <h2>🧪 Test Admin Login</h2>
        <div>
            <button onclick="goToAdminLogin()">📄 Go to Admin Login</button>
            <button onclick="testAdminLogin()">🔑 Test Admin Login</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus() {
            document.getElementById('currentUrl').textContent = window.location.pathname;
        }

        function goToAdminLogin() {
            log('📄 Navigating to /admin/login');
            history.pushState({}, '', '/admin/login');
            updateStatus();
        }

        async function testAdminLogin() {
            const testResult = document.getElementById('testResult');
            const testStatus = document.getElementById('testStatus');
            
            log('=== 🧪 TESTING ADMIN LOGIN ===');
            testResult.textContent = 'Testing...';
            testResult.className = 'status info';
            testStatus.textContent = 'Testing...';
            
            try {
                // Step 1: Navigate to admin login
                log('Step 1: Navigate to /admin/login');
                goToAdminLogin();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Step 2: Check if we're on admin login page
                const currentPath = window.location.pathname;
                if (currentPath !== '/admin/login') {
                    throw new Error(`Expected /admin/login, got ${currentPath}`);
                }
                log('✅ Successfully on admin login page');
                
                // Step 3: Check if only password option is visible
                log('Step 3: Checking if only password option is visible');
                log('🔍 Manual check: Look at the admin login page');
                log('   - Should only see "Password" tab');
                log('   - Should NOT see PIN or Biometric tabs');
                
                // Step 4: Simulate login (this would be done manually in the browser)
                log('Step 4: Manual login test');
                log('🔍 Manual steps:');
                log('   1. Enter <EMAIL>');
                log('   2. Enter admin123');
                log('   3. Click login');
                log('   4. Should redirect to /admin');
                
                // Test completed
                log('✅ Admin login test setup complete');
                log('🔍 Please manually test the login form');
                
                testResult.textContent = '✅ Manual Test Required';
                testResult.className = 'status success';
                testStatus.textContent = 'Manual Test Required';
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`);
                testResult.textContent = '❌ Test Failed';
                testResult.className = 'status error';
                testStatus.textContent = 'Failed';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Simple Admin Login Test initialized');
            log('📋 Test Requirements:');
            log('   1. Admin login page shows only password option');
            log('   2. Successful login redirects to /admin dashboard');
            updateStatus();
        });
    </script>
</body>
</html>
