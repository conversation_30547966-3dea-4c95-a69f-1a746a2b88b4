<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Bypass Prevention Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        .locked { background: #fff3cd; color: #856404; }
        .unlocked { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-section {
            border: 2px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .route-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔒 Session Lock Bypass Prevention Test</h1>
    <p>This test verifies that session lock cannot be bypassed by navigating between routes.</p>

    <div class="container">
        <h2>📍 Current Route Information</h2>
        <div class="route-info">
            <div>Current URL: <span id="currentUrl"></span></div>
            <div>Is Protected Route: <span id="isProtected"></span></div>
            <div>Session Lock State: <span id="lockState"></span></div>
        </div>
        <button onclick="updateRouteInfo()">Refresh Route Info</button>
    </div>

    <div class="container test-section">
        <h2>🧪 Bypass Prevention Test</h2>
        <div>
            <button onclick="testBypassScenario()">🚨 Test Bypass Scenario</button>
            <button onclick="simulateRouteChange('/')">Navigate to Home (/)</button>
            <button onclick="simulateRouteChange('/admin')">Navigate to Admin (/admin)</button>
            <button onclick="simulateRouteChange('/dashboard')">Navigate to Dashboard (/dashboard)</button>
            <button onclick="lockSessionManually()">🔒 Lock Session Manually</button>
        </div>
        <div id="testStatus" class="status">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3001/api/v1';
        let authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************.iHnO_Rbiocv_DcHNMtNBuzpatFAhxnBC9DGdU6GTIi4'; // Use your current token

        // Protected routes (same as frontend)
        const protectedRoutes = ['/admin', '/dashboard', '/profile', '/settings', '/account'];

        // Check if route is protected
        function isProtectedRoute(path = window.location.pathname) {
            return protectedRoutes.some(route => path.startsWith(route));
        }

        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // API helper function
        async function apiCall(method, endpoint, data = null) {
            const url = `${API_BASE}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.hlenergy.v1+json',
                    'Authorization': `Bearer ${authToken}`
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(`${response.status}: ${result.error?.message || response.statusText}`);
                }
                
                return result;
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                throw error;
            }
        }

        // Update route information display
        function updateRouteInfo() {
            const currentPath = window.location.pathname;
            const isProtected = isProtectedRoute(currentPath);
            
            document.getElementById('currentUrl').textContent = currentPath;
            document.getElementById('isProtected').textContent = isProtected ? 'YES' : 'NO';
            document.getElementById('isProtected').style.color = isProtected ? '#dc3545' : '#28a745';
            
            // Check session lock state
            checkSessionLockState();
        }

        // Check current session lock state
        async function checkSessionLockState() {
            try {
                const response = await apiCall('GET', '/auth/session/status');
                const isLocked = response.data.isLocked;
                
                document.getElementById('lockState').textContent = isLocked ? 'LOCKED' : 'UNLOCKED';
                document.getElementById('lockState').style.color = isLocked ? '#dc3545' : '#28a745';
                
                return isLocked;
            } catch (error) {
                document.getElementById('lockState').textContent = 'ERROR';
                document.getElementById('lockState').style.color = '#6c757d';
                return null;
            }
        }

        // Simulate route change
        function simulateRouteChange(newPath) {
            log(`🔄 Simulating navigation to: ${newPath}`);
            
            // Update URL without actually navigating
            history.pushState({}, '', newPath);
            
            // Update display
            updateRouteInfo();
            
            log(`📍 Route changed to: ${newPath} (Protected: ${isProtectedRoute(newPath)})`);
        }

        // Lock session manually
        async function lockSessionManually() {
            log('🔒 Manually locking session...');
            try {
                await apiCall('POST', '/auth/session/lock');
                log('✅ Session locked successfully');
                updateRouteInfo();
            } catch (error) {
                log(`❌ Failed to lock session: ${error.message}`);
            }
        }

        // Test the bypass scenario
        async function testBypassScenario() {
            const testStatus = document.getElementById('testStatus');
            
            log('=== 🚨 TESTING SESSION LOCK BYPASS SCENARIO ===');
            testStatus.textContent = 'Testing in progress...';
            testStatus.className = 'status offline';
            
            try {
                // Step 1: Start on admin page
                log('Step 1: Navigate to /admin (protected route)');
                simulateRouteChange('/admin');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Lock the session
                log('Step 2: Lock the session');
                await lockSessionManually();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 3: Navigate to homepage
                log('Step 3: Navigate to / (public route)');
                simulateRouteChange('/');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 4: Navigate back to dashboard
                log('Step 4: Navigate to /dashboard (protected route)');
                simulateRouteChange('/dashboard');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 5: Check if session is still locked
                log('Step 5: Checking if session lock is still enforced...');
                const isLocked = await checkSessionLockState();
                
                if (isLocked === true) {
                    log('✅ SUCCESS: Session lock is still enforced! Bypass prevented.');
                    testStatus.textContent = '✅ Bypass Prevention Working';
                    testStatus.className = 'status online';
                } else if (isLocked === false) {
                    log('❌ FAILURE: Session lock was bypassed! Security vulnerability detected.');
                    testStatus.textContent = '❌ Bypass Vulnerability Detected';
                    testStatus.className = 'status offline';
                } else {
                    log('⚠️ ERROR: Could not determine session lock state');
                    testStatus.textContent = '⚠️ Test Inconclusive';
                    testStatus.className = 'status locked';
                }
                
            } catch (error) {
                log(`❌ Test failed with error: ${error.message}`);
                testStatus.textContent = '❌ Test Failed';
                testStatus.className = 'status offline';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Session Lock Bypass Prevention Test initialized');
            updateRouteInfo();
            
            // Listen for popstate events (back/forward navigation)
            window.addEventListener('popstate', () => {
                updateRouteInfo();
                log(`📍 Route changed via browser navigation to: ${window.location.pathname}`);
            });
        });
    </script>
</body>
</html>
