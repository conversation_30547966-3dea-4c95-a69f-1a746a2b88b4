<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧭 Basic Navigation Test</h1>
    <p>Testing if basic navigation between login pages works correctly.</p>

    <div class="container">
        <h2>📍 Current Status</h2>
        <div>Current URL: <span id="currentUrl"></span></div>
        <div>Auth Status: <span id="authStatus"></span></div>
        <button onclick="updateStatus()">Refresh Status</button>
    </div>

    <div class="container">
        <h2>🧪 Navigation Tests</h2>
        <div>
            <button onclick="goToLogin()">Go to /login</button>
            <button onclick="goToAdminLogin()">Go to /admin/login</button>
            <button onclick="goToHome()">Go to /</button>
            <button onclick="goToAdmin()">Go to /admin</button>
        </div>
        <div>
            <button onclick="testBasicNavigation()">🚀 Test Basic Navigation</button>
        </div>
        <div id="testResult" class="status info">Ready to test</div>
    </div>

    <div class="container">
        <h2>📋 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus() {
            const currentUrl = window.location.pathname;
            document.getElementById('currentUrl').textContent = currentUrl;
            
            // Check if we can access the auth store (this is just a basic test)
            document.getElementById('authStatus').textContent = 'Testing...';
            document.getElementById('authStatus').style.color = '#6c757d';
        }

        function goToLogin() {
            log('🔄 Navigating to /login');
            history.pushState({}, '', '/login');
            updateStatus();
            
            // Wait a moment and check if we stayed on the page
            setTimeout(() => {
                const currentPath = window.location.pathname;
                if (currentPath === '/login') {
                    log('✅ Successfully navigated to /login');
                } else {
                    log(`❌ Navigation failed - ended up at: ${currentPath}`);
                }
            }, 1000);
        }

        function goToAdminLogin() {
            log('🔄 Navigating to /admin/login');
            history.pushState({}, '', '/admin/login');
            updateStatus();
            
            // Wait a moment and check if we stayed on the page
            setTimeout(() => {
                const currentPath = window.location.pathname;
                if (currentPath === '/admin/login') {
                    log('✅ Successfully navigated to /admin/login');
                } else {
                    log(`❌ Navigation failed - ended up at: ${currentPath}`);
                }
            }, 1000);
        }

        function goToHome() {
            log('🔄 Navigating to /');
            history.pushState({}, '', '/');
            updateStatus();
            
            setTimeout(() => {
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    log('✅ Successfully navigated to /');
                } else {
                    log(`❌ Navigation failed - ended up at: ${currentPath}`);
                }
            }, 1000);
        }

        function goToAdmin() {
            log('🔄 Navigating to /admin');
            history.pushState({}, '', '/admin');
            updateStatus();
            
            setTimeout(() => {
                const currentPath = window.location.pathname;
                if (currentPath === '/admin') {
                    log('✅ Successfully navigated to /admin');
                } else {
                    log(`❌ Navigation failed - ended up at: ${currentPath}`);
                }
            }, 1000);
        }

        async function testBasicNavigation() {
            const testResult = document.getElementById('testResult');
            
            log('=== 🚀 TESTING BASIC NAVIGATION ===');
            testResult.textContent = 'Testing in progress...';
            testResult.className = 'status info';
            
            let allTestsPassed = true;
            
            try {
                // Test 1: Navigate to /login
                log('Test 1: Navigate to /login');
                goToLogin();
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (window.location.pathname !== '/login') {
                    log('❌ Test 1 FAILED: Could not navigate to /login');
                    allTestsPassed = false;
                }
                
                // Test 2: Navigate to /admin/login
                log('Test 2: Navigate to /admin/login');
                goToAdminLogin();
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (window.location.pathname !== '/admin/login') {
                    log('❌ Test 2 FAILED: Could not navigate to /admin/login');
                    allTestsPassed = false;
                }
                
                // Test 3: Navigate to home
                log('Test 3: Navigate to /');
                goToHome();
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (window.location.pathname !== '/') {
                    log('❌ Test 3 FAILED: Could not navigate to /');
                    allTestsPassed = false;
                }
                
                // Test 4: Navigate back to /login
                log('Test 4: Navigate back to /login');
                goToLogin();
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                if (window.location.pathname !== '/login') {
                    log('❌ Test 4 FAILED: Could not navigate back to /login');
                    allTestsPassed = false;
                }
                
                // Final result
                if (allTestsPassed) {
                    log('🎉 ALL NAVIGATION TESTS PASSED!');
                    testResult.textContent = '✅ Navigation Working';
                    testResult.className = 'status success';
                } else {
                    log('❌ SOME NAVIGATION TESTS FAILED');
                    testResult.textContent = '❌ Navigation Broken';
                    testResult.className = 'status error';
                }
                
            } catch (error) {
                log(`❌ Test failed with error: ${error.message}`);
                testResult.textContent = '❌ Test Failed';
                testResult.className = 'status error';
            }
            
            log('=== Test completed ===');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Basic Navigation Test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
